<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

// 推送支付单到esb
$sql = "select * from `sdb_sales_sap_orders` where bill_type = 'delivery' and sync_status = 'none' limit 200";
$orderList = kernel::database()->select($sql);
foreach($orderList as $order) {
	if ($order) {
		push_esb($order);
	}
}

function push_esb($order)
{
    // 判断支付单是否推送
    $sql = "select * from `sdb_sales_sap_orders` where source_bill_id = '{$order['bill_id']}' and bill_type = 'payed' and sync_status = 'succ'";
    $payedList = kernel::database()->select($sql);
    if (!$payedList) {
        echo "支付单未推送, 不推送发货单\n";
        return;
    }

    // 推送发货单
    $sql = "update `sdb_sales_sap_orders` set sync_status = 'fail' where sap_id = {$order['sap_id']} and bill_type = 'delivery' and bill_id = '{$order['bill_id']}' and sync_status = 'none'";
    kernel::database()->exec($sql);

    $delivery_id = $order['bill_id'];
    $sdf = kernel::single('ome_sap_data_platform_wxshipin')->get_delivery_sdf($delivery_id);
    kernel::single('erpapi_router_request')->set('sap', true)->delivery_push($sdf);
}